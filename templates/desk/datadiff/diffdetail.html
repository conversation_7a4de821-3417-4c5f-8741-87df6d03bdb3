{{ define "desk/datadiff/diffdetail.html"}}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>接口Diff详情分析 - fwyytool</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 6 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    <!-- ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- Moment.js -->
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
    
    <style>
        :root {
            /* Apple Design System Colors */
            --apple-blue: #007AFF;
            --apple-blue-light: #5AC8FA;
            --apple-blue-dark: #0051D5;
            --apple-green: #34C759;
            --apple-orange: #FF9500;
            --apple-red: #FF3B30;
            --apple-purple: #AF52DE;
            --apple-pink: #FF2D92;
            --apple-yellow: #FFCC00;
            
            /* Neutral Colors - Apple Style */
            --apple-gray-1: #8E8E93;
            --apple-gray-2: #AEAEB2;
            --apple-gray-3: #C7C7CC;
            --apple-gray-4: #D1D1D6;
            --apple-gray-5: #E5E5EA;
            --apple-gray-6: #F2F2F7;
            
            /* Text Colors */
            --text-primary: #1D1D1F;
            --text-secondary: #86868B;
            --text-tertiary: #AEAEB2;
            --text-link: var(--apple-blue);
            
            /* Background Colors */
            --bg-primary: #FFFFFF;
            --bg-secondary: #F5F5F7;
            --bg-tertiary: var(--apple-gray-6);
            --bg-elevated: #FFFFFF;
            
            /* Apple Spacing System */
            --spacing-2xs: 2px;
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 12px;
            --spacing-lg: 16px;
            --spacing-xl: 20px;
            --spacing-2xl: 24px;
            --spacing-3xl: 32px;
            --spacing-4xl: 40px;
            --spacing-5xl: 48px;
            
            /* Apple Border Radius */
            --radius-xs: 4px;
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-2xl: 20px;
            
            /* Apple Shadows */
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04);
            --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
            --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
            --shadow-xl: 0 16px 40px rgba(0, 0, 0, 0.16);
            
            /* Apple Typography Scale */
            --font-size-xs: 11px;
            --font-size-sm: 13px;
            --font-size-base: 15px;
            --font-size-lg: 17px;
            --font-size-xl: 19px;
            --font-size-2xl: 22px;
            --font-size-3xl: 28px;
            --font-size-4xl: 34px;
            
            /* Font Weights - Apple Style */
            --font-weight-regular: 400;
            --font-weight-medium: 500;
            --font-weight-semibold: 600;
            --font-weight-bold: 700;
        }
        
        body {
            background: var(--bg-secondary);
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Arial, sans-serif;
            color: var(--text-primary);
            font-size: var(--font-size-base);
            line-height: 1.47;
            letter-spacing: -0.022em;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        /* Apple Typography Hierarchy */
        h1, .h1 { 
            font-size: var(--font-size-4xl); 
            font-weight: var(--font-weight-bold); 
            line-height: 1.12; 
            letter-spacing: -0.025em;
            margin-bottom: var(--spacing-lg);
        }
        h2, .h2 { 
            font-size: var(--font-size-3xl); 
            font-weight: var(--font-weight-semibold); 
            line-height: 1.18; 
            letter-spacing: -0.022em;
            margin-bottom: var(--spacing-md);
        }
        h3, .h3 { 
            font-size: var(--font-size-2xl); 
            font-weight: var(--font-weight-semibold); 
            line-height: 1.27; 
            letter-spacing: -0.019em;
            margin-bottom: var(--spacing-md);
        }
        h4, .h4 { 
            font-size: var(--font-size-xl); 
            font-weight: var(--font-weight-medium); 
            line-height: 1.32; 
            letter-spacing: -0.016em;
            margin-bottom: var(--spacing-sm);
        }
        h5, .h5 { 
            font-size: var(--font-size-lg); 
            font-weight: var(--font-weight-medium); 
            line-height: 1.35; 
            letter-spacing: -0.013em;
            margin-bottom: var(--spacing-sm);
        }
        h6, .h6 { 
            font-size: var(--font-size-base); 
            font-weight: var(--font-weight-semibold); 
            line-height: 1.4; 
            letter-spacing: -0.01em;
            margin-bottom: var(--spacing-xs);
        }
        
        /* Apple Text Styles */
        .text-large { font-size: var(--font-size-xl); line-height: 1.32; }
        .text-body { font-size: var(--font-size-base); line-height: 1.47; }
        .text-caption { font-size: var(--font-size-sm); line-height: 1.38; color: var(--text-secondary); }
        .text-footnote { font-size: var(--font-size-xs); line-height: 1.36; color: var(--text-tertiary); }
        
        .page-header {
            background: linear-gradient(180deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            padding: var(--spacing-3xl) 0 var(--spacing-2xl) 0;
            margin-bottom: var(--spacing-2xl);
            border-bottom: 1px solid var(--apple-gray-5);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }
        
        .page-header .container-fluid {
            position: relative;
        }
        
        .page-title {
            font-size: var(--font-size-4xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
            letter-spacing: -0.025em;
        }
        
        .page-subtitle {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-regular);
            color: var(--text-secondary);
            margin-bottom: 0;
            letter-spacing: -0.016em;
        }
        
        .metric-card {
            background: var(--bg-elevated);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            padding: 0;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            border: 1px solid var(--apple-gray-5);
            margin-bottom: 0;
            position: relative;
            overflow: hidden;
            height: 120px;
            display: flex;
            flex-direction: column;
        }
        
        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            transition: all 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
        }
        
        .metric-card-content {
            padding: var(--spacing-xl) var(--spacing-lg);
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex: 1;
        }
        
        .metric-header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }
        
        .metric-body {
            display: flex;
            justify-content: center;
            align-items: center;
            flex: 1;
        }
        
        /* 不同类型卡片的顶部装饰条颜色 */
        .metric-card.card-info::before {
            background: linear-gradient(90deg, #007AFF 0%, #5AC8FA 100%);
        }
        
        .metric-card.card-danger::before {
            background: linear-gradient(90deg, #FF3B30 0%, #FF6B6B 100%);
        }
        
        .metric-card.card-success::before {
            background: linear-gradient(90deg, #34C759 0%, #30D158 100%);
        }
        
        .metric-card.card-warning::before {
            background: linear-gradient(90deg, #FF9500 0%, #FFCC00 100%);
        }
        
        .metric-icon {
            font-size: var(--font-size-lg);
            opacity: 0.8;
        }
        
        .metric-value {
            font-size: var(--font-size-4xl);
            font-weight: var(--font-weight-bold);
            letter-spacing: -0.025em;
            color: var(--text-primary);
        }
        
        .metric-label {
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            letter-spacing: -0.01em;
            margin: 0;
        }
        
        /* 图标颜色与装饰条保持一致 */
        .metric-card.card-info .metric-icon { color: var(--apple-blue); }
        .metric-card.card-danger .metric-icon { color: var(--apple-red); }
        .metric-card.card-success .metric-icon { color: var(--apple-green); }
        .metric-card.card-warning .metric-icon { color: var(--apple-orange); }
        
        .query-panel {
            background: white;
            border-radius: var(--border-radius-md);
            box-shadow: var(--shadow-card);
            margin-bottom: var(--spacing-lg);
            overflow: hidden;
        }
        
        .panel-header {
            background: #f8f9fa;
            padding: var(--spacing-md) var(--spacing-lg);
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .panel-body {
            padding: var(--spacing-lg);
        }
        
        .enhanced-datatable {
            background: var(--bg-primary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-card);
            overflow: hidden;
            border: 1px solid var(--border-color);
        }
        
        .table-controls {
            background: var(--bg-secondary);
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: var(--spacing-md);
        }
        
        .table-controls h5 {
            color: var(--text-primary);
            font-weight: 600;
            margin: 0;
        }
        
        .table-controls .badge {
            background: var(--info-color);
            color: white;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius-sm);
        }
        
        .status-badge {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-xl);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-semibold);
            letter-spacing: -0.01em;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 60px;
        }
        
        .status-0 { 
            background: rgba(255, 149, 0, 0.1);
            color: var(--apple-orange); 
            border: 1px solid rgba(255, 149, 0, 0.2);
        }
        .status-1 { 
            background: rgba(52, 199, 89, 0.1);
            color: var(--apple-green); 
            border: 1px solid rgba(52, 199, 89, 0.2);
        }
        .status-2 { 
            background: rgba(255, 59, 48, 0.1);
            color: var(--apple-red); 
            border: 1px solid rgba(255, 59, 48, 0.2);
        }
        
        .diff-badge {
            padding: 0.25rem 0.5rem;
            border-radius: var(--border-radius-sm);
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .diff-has { background-color: #f8d7da; color: #721c24; }
        .diff-none { background-color: #d4edda; color: #155724; }
        
        .params-preview {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: pointer;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-xs);
        }
        
        .stats-summary {
            background: var(--bg-elevated);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-md);
            padding: var(--spacing-2xl);
            margin-bottom: var(--spacing-2xl);
            border: 1px solid var(--apple-gray-5);
        }
        
        .time-filter-compact {
            background: rgba(255, 255, 255, 0.8);
            border-radius: var(--radius-lg);
            padding: var(--spacing-sm);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--apple-gray-5);
            box-shadow: var(--shadow-sm);
        }
        
        .time-filter-compact .form-select,
        .time-filter-compact .form-control {
            background: var(--bg-primary);
            border: 1px solid var(--apple-gray-4);
            border-radius: var(--radius-sm);
            color: var(--text-primary);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-regular);
            padding: var(--spacing-xs) var(--spacing-sm);
            transition: all 0.2s ease;
        }
        
        .time-filter-compact .form-select:focus,
        .time-filter-compact .form-control:focus {
            border-color: var(--apple-blue);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
            outline: none;
        }
        
        .time-filter-compact .btn {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
        }
        
        .table-controls .form-select-sm,
        .table-controls .form-control-sm {
            font-size: var(--font-size-sm);
            border-radius: var(--radius-sm);
            border: 1px solid var(--apple-gray-4);
            padding: var(--spacing-xs) var(--spacing-sm);
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.2s ease;
        }
        
        .table-controls .form-select-sm:focus,
        .table-controls .form-control-sm:focus {
            border-color: var(--apple-blue);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
            outline: none;
        }
        
        .table-controls .form-select-sm option {
            padding: var(--spacing-xs);
            color: var(--text-primary);
            background-color: var(--bg-primary);
        }
        
        /* DataTables 分页控件样式优化 */
        .dataTables_wrapper {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Arial, sans-serif;
        }
        
        .dataTables_length {
            margin-bottom: var(--spacing-sm);
        }
        
        .dataTables_length label {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .dataTables_length select {
            font-size: var(--font-size-sm);
            padding: var(--spacing-2xs) var(--spacing-xs);
            border: 1px solid var(--apple-gray-4);
            border-radius: var(--radius-sm);
            background-color: var(--bg-primary);
            color: var(--text-primary);
            margin: 0 var(--spacing-xs);
            min-width: 60px;
        }
        
        .dataTables_info {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            padding: var(--spacing-2xs) 0;
            font-weight: var(--font-weight-regular);
        }
        
        .dataTables_paginate {
            padding: var(--spacing-2xs) 0;
        }
        
        .dataTables_paginate .paginate_button {
            font-size: var(--font-size-xs);
            padding: var(--spacing-xs) var(--spacing-xs);
            margin: 0 var(--spacing-2xs);
            border: 1px solid var(--apple-gray-4);
            border-radius: var(--radius-sm);
            background: var(--bg-primary);
            color: var(--text-primary);
            text-decoration: none;
            transition: all 0.2s ease;
            font-weight: var(--font-weight-medium);
        }
        
        .dataTables_paginate .paginate_button:hover {
            background: var(--bg-secondary);
            border-color: var(--apple-gray-3);
            color: var(--text-primary);
            transform: translateY(-1px);
        }
        
        .dataTables_paginate .paginate_button.current {
            background: var(--apple-blue);
            border-color: var(--apple-blue);
            color: white;
            font-weight: var(--font-weight-semibold);
        }
        
        .dataTables_paginate .paginate_button.disabled {
            background: var(--bg-secondary);
            border-color: var(--apple-gray-5);
            color: var(--text-tertiary);
            cursor: not-allowed;
        }
        
        .dataTables_paginate .paginate_button.disabled:hover {
            background: var(--bg-secondary);
            border-color: var(--apple-gray-5);
            color: var(--text-tertiary);
            transform: none;
        }
        
        /* DataTables 底部布局优化 */
        .dataTables_wrapper .row {
            margin: 0;
            padding: var(--spacing-sm) var(--spacing-md);
            background: var(--bg-secondary);
            border-top: 1px solid var(--apple-gray-5);
        }
        
        .dataTables_wrapper .row:last-child {
            border-bottom-left-radius: var(--radius-xl);
            border-bottom-right-radius: var(--radius-xl);
        }
        
        .dataTables_wrapper .col-sm-12,
        .dataTables_wrapper .col-md-5,
        .dataTables_wrapper .col-md-7 {
            padding: 0;
        }
        
        .summary-toggle {
            cursor: pointer;
            color: var(--error-color);
            font-weight: bold;
            text-decoration: none;
        }
        
        .summary-toggle:hover {
            text-decoration: underline;
        }
        
        /* Apple Button System */
        .btn {
            border-radius: var(--radius-sm);
            font-weight: var(--font-weight-medium);
            font-size: var(--font-size-base);
            padding: var(--spacing-sm) var(--spacing-lg);
            transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            border: none;
            letter-spacing: -0.01em;
            position: relative;
            overflow: hidden;
        }
        
        .btn-sm {
            font-size: var(--font-size-sm);
            padding: var(--spacing-xs) var(--spacing-md);
        }
        
        .btn-primary {
            background: var(--apple-blue);
            color: white;
            box-shadow: var(--shadow-sm);
        }
        
        .btn-primary:hover {
            background: var(--apple-blue-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
            color: white;
        }
        
        .btn-primary:active {
            transform: translateY(0);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-outline-primary {
            border: 1px solid var(--apple-blue);
            color: var(--apple-blue);
            background: transparent;
        }
        
        .btn-outline-primary:hover {
            background: var(--apple-blue);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-outline-secondary {
            border: 1px solid var(--apple-gray-4);
            color: var(--text-secondary);
            background: var(--bg-primary);
        }
        
        .btn-outline-secondary:hover {
            background: var(--bg-secondary);
            border-color: var(--apple-gray-3);
            color: var(--text-primary);
            transform: translateY(-1px);
        }
        
        /* Apple Table System */
        .table {
            border-collapse: separate;
            border-spacing: 0;
            font-size: var(--font-size-base);
        }
        
        .table thead th {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: none;
            border-bottom: 1px solid var(--apple-gray-4);
            font-weight: var(--font-weight-semibold);
            font-size: var(--font-size-sm);
            padding: var(--spacing-md) var(--spacing-sm);
            letter-spacing: -0.01em;
            text-align: left;
        }
        
        .table tbody tr {
            transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            border-bottom: 1px solid var(--apple-gray-5);
        }
        
        .table tbody tr:hover {
            background: rgba(0, 122, 255, 0.04);
            transform: translateY(-1px);
        }
        
        .table tbody tr:last-child {
            border-bottom: none;
        }
        
        .table tbody td {
            padding: var(--spacing-md) var(--spacing-sm);
            border-top: none;
            vertical-align: middle;
            font-size: var(--font-size-base);
            color: var(--text-primary);
        }
        
        .enhanced-datatable {
            background: var(--bg-elevated);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-md);
            overflow: hidden;
            border: 1px solid var(--apple-gray-5);
        }
        
        .table-controls {
            background: var(--bg-secondary);
            padding: var(--spacing-lg) var(--spacing-xl);
            border-bottom: 1px solid var(--apple-gray-5);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: var(--spacing-md);
        }
        
        /* Apple Responsive Design */
        @media (max-width: 768px) {
            .page-header {
                padding: var(--spacing-2xl) 0 var(--spacing-xl) 0;
            }
            
            .page-title {
                font-size: var(--font-size-3xl);
            }
            
            .page-subtitle {
                font-size: var(--font-size-base);
            }
            
            .table-controls {
                flex-direction: column;
                align-items: stretch;
                padding: var(--spacing-lg);
            }
            
            .metric-card {
                height: 110px;
            }
            
            .metric-card-content {
                padding: var(--spacing-lg) var(--spacing-md);
            }
            
            .metric-value {
                font-size: var(--font-size-3xl);
            }
            
            .time-filter-compact .d-flex {
                flex-direction: column;
                gap: var(--spacing-sm);
            }
            
            .custom-time-range .d-flex {
                flex-direction: column;
                gap: var(--spacing-xs);
            }
            
            .time-filter-compact .form-select,
            .time-filter-compact .form-control {
                width: 100% !important;
            }
            
            /* 移动端表格内边距调整 */
            .table thead th,
            .table tbody td {
                padding: var(--spacing-lg) var(--spacing-md);
            }
            
            /* 移动端DataTables样式调整 */
            .dataTables_length label {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-xs);
            }
            
            .dataTables_length select {
                margin: 0;
                width: 100px;
            }
            
            .dataTables_paginate .paginate_button {
                padding: var(--spacing-xs) var(--spacing-sm);
                margin: 0 1px;
                font-size: var(--font-size-xs);
            }
        }
        
        @media (max-width: 480px) {
            .page-header .d-flex {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-lg);
            }
            
            .time-filter-compact {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <div class="page-header">
        <div class="container-fluid">
                        <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h1 class="page-title mb-0">
                        接口回放Diff详情分析
                    </h1>
                    <p class="page-subtitle">
                        实时监控接口数据差异，精准定位问题根源
                    </p>
                </div>
                <div class="text-end">
                    <div class="d-flex align-items-center gap-3">
                        <!-- 时间范围筛选器 -->
                        <div class="time-filter-compact">
                            <div class="d-flex align-items-center gap-2">
                                <select class="form-select form-select-sm" id="timePreset" style="width: 120px;">
                                    <option value="24h">最近24小时</option>
                                    <option value="7d">最近7天</option>
                                    <option value="30d">最近30天</option>
                                    <option value="custom">自定义</option>
                                </select>
                                <div class="custom-time-range" id="customTimeRange" style="display: none;">
                                    <div class="d-flex align-items-center gap-1">
                                        <input type="datetime-local" class="form-control form-control-sm" id="startTime" style="width: 140px;">
                                        <span class="text-muted small">至</span>
                                        <input type="datetime-local" class="form-control form-control-sm" id="endTime" style="width: 140px;">
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary btn-sm" id="applyTimeFilter">
                                    <i class="fas fa-filter"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container-fluid">
        <!-- 数据概览 -->
        <div class="row mb-4 g-3">
            <div class="col-lg-3 col-md-6">
                <div class="metric-card card-info">
                    <div class="metric-card-content">
                        <div class="metric-header">
                            <div class="metric-icon">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <div class="metric-label">总任务数</div>
                        </div>
                        <div class="metric-body">
                            <div class="metric-value">{{.data.Total}}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="metric-card card-danger">
                    <div class="metric-card-content">
                        <div class="metric-header">
                            <div class="metric-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="metric-label">有差异任务</div>
                        </div>
                        <div class="metric-body">
                            <div class="metric-value">{{.data.HasDiffNum}}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="metric-card card-success">
                    <div class="metric-card-content">
                        <div class="metric-header">
                            <div class="metric-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="metric-label">无差异任务</div>
                        </div>
                        <div class="metric-body">
                            <div class="metric-value">{{.data.NoDiffNum}}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="metric-card card-warning">
                    <div class="metric-card-content">
                        <div class="metric-header">
                            <div class="metric-icon">
                                <i class="fas fa-hourglass-half"></i>
                            </div>
                            <div class="metric-label">未完成任务</div>
                        </div>
                        <div class="metric-body">
                            <div class="metric-value">{{.data.UnFinishedTask}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="stats-summary mb-3">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="mb-0 fw-bold">
                    <i class="fas fa-chart-bar me-2 text-primary"></i>
                    统计概览
                    <small class="text-muted ms-2" id="statsTimeRange">（最近24小时）</small>
                </h6>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="toggleStatsOverview()">
                    <i class="fas fa-chevron-down me-1" id="statsToggleIcon"></i>
                    <span id="statsToggleText">展开</span>
                </button>
            </div>
            <div id="statsOverviewData" class="collapse">
                <div class="text-center py-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 增强数据表格 -->
        <div class="enhanced-datatable">
            <div class="table-controls">
                <div class="d-flex align-items-center justify-content-between w-100">
                <div class="d-flex align-items-center">
                    <h5 class="mb-0 me-3" style="font-weight: var(--font-weight-semibold); color: var(--text-primary);">
                        Diff结果列表
                    </h5>
                    <span class="badge" style="background: rgba(0, 122, 255, 0.1); color: var(--apple-blue); border: 1px solid rgba(0, 122, 255, 0.2); font-size: var(--font-size-xs); padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm);">前100条差异记录</span>
                </div>
                    <div class="d-flex align-items-center gap-3">
                        <!-- 接口筛选 -->
                        <div class="interface-filter">
                            <select class="form-select form-select-sm" id="interfaceFilter" style="width: 220px; max-width: 220px;">
                                <option value="">全部接口</option>
                                <!-- 选项通过loadInterfaceList()动态加载 -->
                            </select>
                        </div>
                        <!-- 状态筛选 -->
                        <div class="status-filter">
                            <select class="form-select form-select-sm" id="statusFilter" style="width: 120px;">
                                <option value="">全部状态</option>
                                <option value="0">未完成</option>
                                <option value="1">已完成</option>
                                <option value="2">失败</option>
                            </select>
                        </div>
                        <!-- 搜索框 -->
                <div class="table-search">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control form-control-sm" id="tableSearch" placeholder="搜索接口名、指纹..." style="width: 200px;">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="table-responsive">
                <table id="diffDataTable" class="table table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th width="200">接口信息</th>
                            <th width="180">请求参数</th>
                            <th width="80">差异数</th>
                            <th width="150">旧数据</th>
                            <th width="150">新数据</th>
                            <th width="120">状态</th>
                            <th width="140">更新时间</th>
                            <th width="120">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {{range $key, $diffInfo := .data.DataDiffList}}
                        <tr data-id="{{$diffInfo.ID}}" data-status="{{$diffInfo.Status}}" data-diff-type="{{$diffInfo.DiffType}}">
                            <td>
                                <div class="fw-bold text-primary">{{$diffInfo.HandlerName}}</div>
                                <small class="text-muted font-monospace">{{$diffInfo.Fingerprint}}</small>
                                <div class="mt-1">
                                    <span class="badge bg-secondary">{{if eq $diffInfo.DiffType 0}}新老对比{{else}}环比对比{{end}}</span>
                                </div>
                            </td>
                            <td>
                                <div class="params-preview" onclick="showParamsModal('{{$diffInfo.ID}}', '{{js $diffInfo.Params}}')" title="点击查看完整参数">
                                    {{if $diffInfo.Params}}
                                        {{$diffInfo.Params | printf "%.100s"}}
                                        {{if gt (len $diffInfo.Params) 100}}...{{end}}
                                    {{else}}
                                        <span class="text-muted">无参数</span>
                                    {{end}}
                                </div>
                            </td>
                            <td>
                                <span class="badge {{if gt $diffInfo.DiffNum 0}}bg-danger{{else}}bg-success{{end}} fs-6">
                                    {{$diffInfo.DiffNum}}
                                </span>
                            </td>
                            <td>
                                <button class="btn btn-outline-secondary btn-sm" onclick="showDataModal('{{$diffInfo.ID}}', 'old', '{{js $diffInfo.OldData}}')">
                                    <i class="fas fa-eye me-1"></i>查看
                                </button>
                            </td>
                            <td>
                                <button class="btn btn-outline-secondary btn-sm" onclick="showDataModal('{{$diffInfo.ID}}', 'new', '{{js $diffInfo.NewData}}')">
                                    <i class="fas fa-eye me-1"></i>查看
                                </button>
                            </td>
                            <td>
                                <span class="status-badge status-{{$diffInfo.Status}}">
                                    {{if eq $diffInfo.Status 0}}未完成
                                    {{else if eq $diffInfo.Status 1}}已完成
                                    {{else if eq $diffInfo.Status 2}}失败
                                    {{else}}未知{{end}}
                                </span>
                            </td>
                            <td>
                                <div class="text-nowrap">
                                    {{showTime $diffInfo.UpdateTime}}
                                </div>
                                <small class="text-muted">
                                    创建: {{showTime $diffInfo.CreateTime}}
                                </small>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-outline-primary btn-sm" onclick="viewDiffDetail('{{$diffInfo.ID}}', '{{js $diffInfo.Params}}', '{{js $diffInfo.OldData}}', '{{js $diffInfo.NewData}}', '{{$diffInfo.DiffResult}}')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    {{if $diffInfo.DiffResult}}
                                    <a href="{{$diffInfo.DiffResult}}" class="btn btn-outline-success btn-sm" target="_blank" title="查看Diff结果">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    {{end}}
                                </div>
                            </td>
                        </tr>
                        {{end}}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <!-- 数据查看模态框 -->
    <div class="modal fade" id="dataModal" tabindex="-1" aria-labelledby="dataModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="dataModalLabel">
                        <i class="fas fa-code me-2"></i>
                        数据详情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="json-viewer">
                        <pre id="dataModalContent"></pre>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="copyToClipboard()">
                        <i class="fas fa-copy me-1"></i>复制
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Diff结果模态框 -->
    <div class="modal fade" id="diffDetailModal" tabindex="-1" aria-labelledby="diffDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="diffDetailModalLabel">
                        <i class="fas fa-code-branch me-2"></i>
                        Diff结果对比
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="diffTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="params-tab" data-bs-toggle="tab" data-bs-target="#params" type="button" role="tab">
                                <i class="fas fa-cog me-1"></i>请求参数
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="old-data-tab" data-bs-toggle="tab" data-bs-target="#old-data" type="button" role="tab">
                                <i class="fas fa-history me-1"></i>旧数据
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="new-data-tab" data-bs-toggle="tab" data-bs-target="#new-data" type="button" role="tab">
                                <i class="fas fa-plus me-1"></i>新数据
                            </button>
                        </li>
                    </ul>
                    <div class="tab-content" id="diffTabContent">
                        <div class="tab-pane fade show active" id="params" role="tabpanel">
                            <div class="json-viewer mt-3">
                                <pre id="diffParams"></pre>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="old-data" role="tabpanel">
                            <div class="json-viewer mt-3">
                                <pre id="diffOldData"></pre>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="new-data" role="tabpanel">
                            <div class="json-viewer mt-3">
                                <pre id="diffNewData"></pre>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <a id="diffResultLink" href="#" class="btn btn-primary" target="_blank">
                        <i class="fas fa-external-link-alt me-1"></i>查看Diff结果
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS 和依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- DataTables -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        let statsOverviewLoaded = false;
        let currentModalContent = '';
        let currentTimeRange = { preset: '24h', startTime: null, endTime: null };
        
        // 初始化页面
        $(document).ready(function() {
            // 初始化时间显示
            moment.locale('zh-cn');
            
            // 初始化全局时间筛选器
            initTimeFilter();
            
            // 加载接口列表
            loadInterfaceList();
            
            // 初始化DataTables
            $('#diffDataTable').DataTable({
                responsive: true,
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.13.7/i18n/zh.json'
                },
                order: [[6, 'desc']], // 按更新时间降序
                pageLength: 25,
                searching: false // 禁用内置搜索，使用自定义搜索
            });
            
            // 绑定筛选事件
            bindFilterEvents();
            
            // 设置默认时间范围并加载数据
            setDefaultTimeRange();
        });
        
        // 初始化时间筛选器
        function initTimeFilter() {
            $('#timePreset').on('change', function() {
                const preset = $(this).val();
                if (preset === 'custom') {
                    $('#customTimeRange').show();
                    setCustomTimeInputs();
                } else {
                    $('#customTimeRange').hide();
                    updateTimeRangeByPreset(preset);
                }
            });
            
            $('#applyTimeFilter').on('click', function() {
                applyTimeFilter();
            });
        }
        
        // 设置默认时间范围
        function setDefaultTimeRange() {
            updateTimeRangeByPreset('24h');
            $('#timePreset').val('24h');
        }
        
        // 根据预设更新时间范围
        function updateTimeRangeByPreset(preset) {
            const now = moment();
            let startTime, endTime;
            
            switch(preset) {
                case '24h':
                    startTime = now.clone().subtract(24, 'hours');
                    endTime = now;
                    break;
                case '7d':
                    startTime = now.clone().subtract(7, 'days');
                    endTime = now;
                    break;
                case '30d':
                    startTime = now.clone().subtract(30, 'days');
                    endTime = now;
                    break;
            }
            
            currentTimeRange = {
                preset: preset,
                startTime: startTime.unix(),
                endTime: endTime.unix()
            };
            
            updateStatsTimeRangeDisplay(preset);
        }
        
        // 设置自定义时间输入框
        function setCustomTimeInputs() {
            const now = moment();
            const yesterday = now.clone().subtract(1, 'day');
            
            $('#startTime').val(yesterday.format('YYYY-MM-DDTHH:mm'));
            $('#endTime').val(now.format('YYYY-MM-DDTHH:mm'));
        }
        
        // 应用时间筛选
        function applyTimeFilter() {
            const preset = $('#timePreset').val();
            
            if (preset === 'custom') {
                const startTimeStr = $('#startTime').val();
                const endTimeStr = $('#endTime').val();
                
                if (!startTimeStr || !endTimeStr) {
                    alert('请选择开始时间和结束时间');
                    return;
                }
                
                const startTime = moment(startTimeStr);
                const endTime = moment(endTimeStr);
                
                if (startTime.isAfter(endTime)) {
                    alert('开始时间不能晚于结束时间');
                    return;
                }
                
                currentTimeRange = {
                    preset: 'custom',
                    startTime: startTime.unix(),
                    endTime: endTime.unix()
                };
                
                updateStatsTimeRangeDisplay('custom', startTime, endTime);
            }
            
            // 重新加载所有数据
            reloadAllData();
        }
        
        // 更新统计时间范围显示
        function updateStatsTimeRangeDisplay(preset, startTime = null, endTime = null) {
            let displayText = '';
            switch(preset) {
                case '24h':
                    displayText = '（最近24小时）';
                    break;
                case '7d':
                    displayText = '（最近7天）';
                    break;
                case '30d':
                    displayText = '（最近30天）';
                    break;
                case 'custom':
                    if (startTime && endTime) {
                        displayText = `（${startTime.format('MM-DD HH:mm')} 至 ${endTime.format('MM-DD HH:mm')}）`;
                    }
                    break;
            }
            $('#statsTimeRange').text(displayText);
        }
        
        // 重新加载所有数据
        function reloadAllData() {
            // 构建查询参数
            const params = new URLSearchParams();
            params.append('startTime', currentTimeRange.startTime);
            params.append('endTime', currentTimeRange.endTime);
            
            // 重新加载页面数据
            window.location.href = window.location.pathname + '?' + params.toString();
        }
        
        // 绑定筛选事件
        function bindFilterEvents() {
            // 接口筛选
            $('#interfaceFilter').on('change', function() {
                applyTableFilters();
            });
            
            // 状态筛选
            $('#statusFilter').on('change', function() {
                applyTableFilters();
            });
            
            // 搜索框
            $('#tableSearch').on('keyup', function() {
                applyTableFilters();
            });
        }
        
        // 应用表格筛选
        function applyTableFilters() {
            const interfaceFilter = $('#interfaceFilter').val().toLowerCase();
            const statusFilter = $('#statusFilter').val();
            const searchTerm = $('#tableSearch').val().toLowerCase();
            
                $('#diffDataTable tbody tr').each(function() {
                    const row = $(this);
                const interfaceName = row.find('td:first .fw-bold').text().toLowerCase();
                const status = row.data('status').toString();
                const rowText = row.text().toLowerCase();
                
                let showRow = true;
                
                // 接口筛选
                if (interfaceFilter && interfaceName.indexOf(interfaceFilter) === -1) {
                    showRow = false;
                }
                
                // 状态筛选
                if (statusFilter && status !== statusFilter) {
                    showRow = false;
                }
                
                // 文本搜索
                if (searchTerm && rowText.indexOf(searchTerm) === -1) {
                    showRow = false;
                }
                
                if (showRow) {
                        row.show();
                    } else {
                        row.hide();
                    }
                });
        }
        
        // 加载接口列表
        function loadInterfaceList() {
            fetch('/fwyytool/desk/datadiff/handlerlist')
                .then(response => response.json())
                .then(data => {
                    if (data.errNo === 0 && Array.isArray(data.data)) {
                        const select = document.getElementById('interfaceFilter');
                        
                        // 清空现有选项（保留第一个空选项）
                        while (select.children.length > 1) {
                            select.removeChild(select.lastChild);
                        }
                        
                        // 添加从API获取的选项
                        data.data.forEach(handler => {
                            const option = document.createElement('option');
                            option.value = handler;
                            option.textContent = handler;
                            select.appendChild(option);
                        });
                        
                        console.log('接口列表加载成功，共', data.data.length, '个接口');
                    } else {
                        console.error('加载接口列表失败:', data.errMsg || '未知错误');
                    }
                })
                .catch(error => {
                    console.error('请求接口列表失败:', error);
                });
        }
        
        // 切换统计概览
        function toggleStatsOverview() {
            const container = document.getElementById('statsOverviewData');
            const toggleText = document.getElementById('statsToggleText');
            const toggleIcon = document.getElementById('statsToggleIcon');
            
            if (container.classList.contains('show')) {
                container.classList.remove('show');
                toggleText.textContent = '展开';
                toggleIcon.className = 'fas fa-chevron-down me-1';
                return;
            }
            
            if (statsOverviewLoaded) {
                container.classList.add('show');
                toggleText.textContent = '收起';
                toggleIcon.className = 'fas fa-chevron-up me-1';
                return;
            }
            
            // 构建请求参数（支持时间范围）
            const params = new URLSearchParams();
            if (currentTimeRange.startTime && currentTimeRange.endTime) {
                params.append('startTime', currentTimeRange.startTime);
                params.append('endTime', currentTimeRange.endTime);
            }
            
            // 加载数据
            const url = '/fwyytool/desk/datadiff/diffcount' + (params.toString() ? '?' + params.toString() : '');
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.errNo === 0 && data.data) {
                        const statsData = data.data;
                        const sortableData = Object.entries(statsData).map(([key, value]) => ({
                            name: key,
                            ...value
                        })).sort((a, b) => b.hasDiffCnt - a.hasDiffCnt);
                        
                        let tableHtml = `
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-success">
                                        <tr>
                                            <th>接口名称</th>
                                            <th class="text-center">有差异</th>
                                            <th class="text-center">无差异</th>
                                            <th class="text-center">未完成</th>
                                            <th class="text-center">失败</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                        `;
                        
                        sortableData.forEach(item => {
                            const hasDiffClass = item.hasDiffCnt > 0 ? 'text-danger fw-bold' : '';
                            tableHtml += `
                                <tr>
                                    <td class="font-monospace">${item.name}</td>
                                    <td class="text-center ${hasDiffClass}">${item.hasDiffCnt}</td>
                                    <td class="text-center">${item.noDiffCnt}</td>
                                    <td class="text-center">${item.unFinishCnt}</td>
                                    <td class="text-center">${item.failedCnt}</td>
                                </tr>
                            `;
                        });
                        
                        tableHtml += '</tbody></table></div>';
                        container.innerHTML = tableHtml;
                    } else {
                        container.innerHTML = '<div class="alert alert-warning">数据加载失败</div>';
                    }
                    
                    container.classList.add('show');
                    toggleText.textContent = '收起';
                    toggleIcon.className = 'fas fa-chevron-up me-1';
                    statsOverviewLoaded = true;
                })
                .catch(error => {
                    container.innerHTML = `<div class="alert alert-danger">获取数据失败: ${error.message}</div>`;
                    container.classList.add('show');
                    toggleText.textContent = '收起';
                    toggleIcon.className = 'fas fa-chevron-up me-1';
                });
        }
        
        // 显示参数模态框
        function showParamsModal(id, params) {
            try {
                const formatted = JSON.stringify(JSON.parse(params), null, 2);
                document.getElementById('dataModalContent').textContent = formatted;
                document.getElementById('dataModalLabel').innerHTML = '<i class="fas fa-cog me-2"></i>请求参数 - ID: ' + id;
                currentModalContent = formatted;
                new bootstrap.Modal(document.getElementById('dataModal')).show();
            } catch (e) {
                document.getElementById('dataModalContent').textContent = params;
                document.getElementById('dataModalLabel').innerHTML = '<i class="fas fa-cog me-2"></i>请求参数 - ID: ' + id;
                currentModalContent = params;
                new bootstrap.Modal(document.getElementById('dataModal')).show();
            }
        }
        
        // 显示数据模态框
        function showDataModal(id, type, data) {
            try {
                const formatted = JSON.stringify(JSON.parse(data), null, 2);
                document.getElementById('dataModalContent').textContent = formatted;
                const title = type === 'old' ? '旧数据' : '新数据';
                document.getElementById('dataModalLabel').innerHTML = `<i class="fas fa-database me-2"></i>${title} - ID: ${id}`;
                currentModalContent = formatted;
                new bootstrap.Modal(document.getElementById('dataModal')).show();
            } catch (e) {
                document.getElementById('dataModalContent').textContent = data;
                const title = type === 'old' ? '旧数据' : '新数据';
                document.getElementById('dataModalLabel').innerHTML = `<i class="fas fa-database me-2"></i>${title} - ID: ${id}`;
                currentModalContent = data;
                new bootstrap.Modal(document.getElementById('dataModal')).show();
            }
        }
        
        // 显示Diff详情
        function viewDiffDetail(id, params, oldData, newData, diffResult) {
            try {
                // 格式化JSON
                const formatJSON = (str) => {
                    try {
                        return JSON.stringify(JSON.parse(str), null, 2);
                    } catch {
                        return str;
                    }
                };
                
                document.getElementById('diffParams').textContent = formatJSON(params);
                document.getElementById('diffOldData').textContent = formatJSON(oldData);
                document.getElementById('diffNewData').textContent = formatJSON(newData);
                
                const linkBtn = document.getElementById('diffResultLink');
                if (diffResult) {
                    linkBtn.href = diffResult;
                    linkBtn.style.display = 'inline-block';
                } else {
                    linkBtn.style.display = 'none';
                }
                
                new bootstrap.Modal(document.getElementById('diffDetailModal')).show();
            } catch (e) {
                console.error('显示Diff详情错误:', e);
            }
        }
        
        // 复制到剪贴板
        function copyToClipboard() {
            navigator.clipboard.writeText(currentModalContent).then(() => {
                // 显示成功提示
                const toast = document.createElement('div');
                toast.className = 'toast-container position-fixed top-0 end-0 p-3';
                toast.innerHTML = `
                    <div class="toast show" role="alert">
                        <div class="toast-header">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong class="me-auto">成功</strong>
                            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                        </div>
                        <div class="toast-body">内容已复制到剪贴板</div>
                    </div>
                `;
                document.body.appendChild(toast);
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 3000);
            }).catch(err => {
                console.error('复制失败:', err);
            });
        }
    </script>
</body>
</html>
{{end}}