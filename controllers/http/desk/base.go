package desk

import (
	"fwyytool/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterHandlers(rg *gin.RouterGroup) {
	deskRouterGroup := rg.Group("desk", middleware.StrictAuth())
	arkRouterGroup := deskRouterGroup.Group("ark")
	{
		arkRouterGroup.GET("detail", ArkController.Detail)
		arkRouterGroup.GET("detailapi", ArkController.DetailApi)
		arkRouterGroup.GET("jumptasklist", ArkController.JumpTaskList)
		arkRouterGroup.GET("jumptasklistapi", ArkController.JumpTaskListApi)
	}
	arkTestCaseRouterGroup := deskRouterGroup.Group("arktestcase")
	{
		arkTestCaseRouterGroup.GET("rule", ArkTestCaseController.GetArkTestCaseByRuleId)
		arkTestCaseRouterGroup.GET("cname", ArkTestCaseController.GetArkTestCaseByCname)
		arkTestCaseRouterGroup.GET("tool", ArkTestCaseController.GetArkTestCaseByToolId)
	}

	courseRouterGroup := deskRouterGroup.Group("course")
	{
		courseRouterGroup.GET("detail", CourseController.Detail)
	}

	monitorRouterGroup := rg.Group("monitor")
	teacherRouterGroup := monitorRouterGroup.Group("teacher")
	{
		teacherRouterGroup.GET("today", MonitorController.Today)
	}

	dataDiffRouterGroup := deskRouterGroup.Group("datadiff")
	{
		dataDiffRouterGroup.GET("diffdetail", DataDiffController.DataDiffDetail)
		dataDiffRouterGroup.GET("handlerlist", DataDiffController.GetHandlerList)
		dataDiffRouterGroup.GET("diffcount", DataDiffController.GetDiffCount)
	}

	downGradeRouterGroup := deskRouterGroup.Group("downgrade")
	{
		downGradeRouterGroup.GET("getconfig", ArkFieldDownGradeController.GetArkFieldDownGradeConfig)
	}
	{
		downGradeRouterGroup.GET("getfusing", ArkFieldDownGradeController.GetNewArkFieldDownGradeConfig)
	}

	cacheToolRouterGroup := deskRouterGroup.Group("cachetool")
	{
		cacheToolRouterGroup.GET("delarkcache", ArkCacheController.DelAllArkCache)
	}
	{
		cacheToolRouterGroup.GET("delcachebykey", ArkCacheController.DelCacheByKey)
	}

	oplogGroup := deskRouterGroup.Group("oplog")
	{
		oplogGroup.GET("list", OplogController.List)
	}

	fwyyEvaluate := deskRouterGroup.Group("/fwyyevaluate")
	{
		fwyyEvaluate.GET("/textconfig", FwyyEvaluateController.TextConfig)
		fwyyEvaluate.GET("/coursemapping", FwyyEvaluateController.CourseMapping)
	}

	userRouterGroup := deskRouterGroup.Group("user")
	{
		userRouterGroup.GET("convertxuidapi", UserController.ConvertXuidToPersonUidApi)
	}
}
