package desk

import (
	"fwyytool/api/arkgo"
	"fwyytool/libs/json"
	"fwyytool/service/desk"
	"net/http"

	"github.com/gin-gonic/gin"
)

var DataDiffController dataDiffController

type dataDiffController struct {
}

func (s dataDiffController) DataDiffDetail(ctx *gin.Context) {
	errMsg := ""
	var params struct {
		TimeRange int64  `json:"timeRange" form:"timeRange"`
		Handler   string `json:"handler" form:"handler"`
		StartTime int64  `json:"startTime" form:"startTime"` // 新增：自定义开始时间
		EndTime   int64  `json:"endTime" form:"endTime"`     // 新增：自定义结束时间
	}

	if err := ctx.ShouldBind(&params); err != nil {
		errMsg = err.Error()
	}

	data, err := desk.ArkDataDiffService.GetDiffRes(ctx, params.TimeRange, params.<PERSON><PERSON>, params.StartTime, params.EndTime)
	if err != nil {
		errMsg = err.Error()
	}
	dataJson, _ := json.MarshalToString(data)

	output := gin.H{
		"data":     data,
		"dataJson": dataJson,
		"errMsg":   errMsg,
		"params":   params,
	}
	ctx.HTML(http.StatusOK, "desk/datadiff/diffdetail.html", output)
}

// GetHandlerList 获取可用的handler列表
func (s dataDiffController) GetHandlerList(ctx *gin.Context) {
	// 调用arkgo接口获取diff报告，从中提取handler列表
	resp, err := arkgo.NewClient().GetDiffReportRes(ctx)
	if err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"errNo":  1,
			"errMsg": err.Error(),
			"data":   nil,
		})
		return
	}

	// 提取handler列表
	handlerList := make([]string, 0, len(resp))
	for handler := range resp {
		handlerList = append(handlerList, handler)
	}

	ctx.JSON(http.StatusOK, gin.H{
		"errNo":  0,
		"errMsg": "",
		"data":   handlerList,
	})
}

// GetDiffCount 获取差异统计数据
func (s dataDiffController) GetDiffCount(ctx *gin.Context) {
	var params struct {
		StartTime int64 `json:"startTime" form:"startTime"` // 开始时间
		EndTime   int64 `json:"endTime" form:"endTime"`     // 结束时间
	}

	if err := ctx.ShouldBind(&params); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"errNo":  1,
			"errMsg": err.Error(),
			"data":   nil,
		})
		return
	}

	// 调用arkgo接口获取diff统计数据
	resp, err := arkgo.NewClient().GetDiffCountRes(ctx, arkgo.GetDiffCountParams{
		StartTime: params.StartTime,
		EndTime:   params.EndTime,
	})
	if err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"errNo":  1,
			"errMsg": err.Error(),
			"data":   nil,
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"errNo":  0,
		"errMsg": "",
		"data":   resp,
	})
}
